"use client";

import {
  Cancel<PERSON>utton,
  DeleteButton,
  FormActions,
  FormCard,
  FormGrid,
  Input,
  Select,
  SubmitButton,
} from "@/lib/forms";
import Link from "next/link";
import { useEffect, useState } from "react";

interface User {
  id: number;
  email: string;
  name: string | null;
  role: "ADMIN" | "MANAGER" | "CASHIER";
  createdAt: string;
  updatedAt: string;
}

interface UserFormData {
  name: string;
  email: string;
  role: "ADMIN" | "MANAGER" | "CASHIER";
}

export default function UserDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const [user, setUser] = useState<User | null>(null);
  const [formData, setFormData] = useState<UserFormData>({
    name: "",
    email: "",
    role: "CASHIER",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const roleOptions = [
    { value: "CASHIER", label: "Cashier" },
    { value: "MANAGER", label: "Manager" },
    { value: "ADMIN", label: "Admin" },
  ];

  // Load user data
  useEffect(() => {
    const loadUser = async () => {
      try {
        const resolvedParams = await params;
        const response = await fetch(`/api/users/${resolvedParams.id}`);
        if (!response.ok) {
          throw new Error("User not found");
        }
        const userData = await response.json();
        setUser(userData);
        setFormData({
          name: userData.name || "",
          email: userData.email,
          role: userData.role,
        });
      } catch (error) {
        console.error("Error loading user:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, [params]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});

    // Basic validation
    const newErrors: Record<string, string> = {};
    if (!formData.name.trim()) newErrors.name = "Name is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    if (!/\S+@\S+\.\S+/.test(formData.email))
      newErrors.email = "Email is invalid";

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    try {
      const resolvedParams = await params;
      const response = await fetch(`/api/users/${resolvedParams.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.fieldErrors) {
          setErrors(errorData.fieldErrors);
        } else {
          setErrors({ general: errorData.error || "Failed to update user" });
        }
        return;
      }

      // Success - redirect to users list
      window.location.href = "/users";
    } catch {
      setErrors({ general: "Failed to update user" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof UserFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleDelete = async () => {
    if (confirm("Are you sure you want to delete this user?")) {
      try {
        const resolvedParams = await params;
        const response = await fetch(`/api/users/${resolvedParams.id}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error("Failed to delete user");
        }

        // Redirect to users list
        window.location.href = "/users";
      } catch (error) {
        console.error("Error deleting user:", error);
        setErrors({ general: "Failed to delete user" });
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="py-12 text-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          User not found
        </h2>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          The user you're looking for doesn&apos;t exist.
        </p>
        <Link
          href="/users"
          className="mt-4 inline-flex items-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-600 hover:bg-blue-200"
        >
          Back to Users
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link
                    href="/users"
                    className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    Users
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg
                      className="h-5 w-5 flex-shrink-0 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                      {user.name || user.email}
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
              Edit User
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Update user information and role
            </p>
          </div>
        </div>
      </div>

      {/* User Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {errors.general && (
          <div className="rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
            <p className="text-sm text-red-600 dark:text-red-400">
              {errors.general}
            </p>
          </div>
        )}

        <FormCard
          title="User Information"
          description="Basic user account details"
        >
          <FormGrid cols={2} gap="md">
            <Input
              name="name"
              label="Full Name"
              placeholder="Enter full name"
              required
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              error={errors.name}
            />

            <Input
              name="email"
              label="Email Address"
              type="email"
              placeholder="Enter email address"
              required
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              error={errors.email}
            />

            <div className="sm:col-span-2">
              <Select
                name="role"
                label="Role"
                options={roleOptions}
                value={formData.role}
                onChange={(e) =>
                  handleInputChange(
                    "role",
                    e.target.value as UserFormData["role"],
                  )
                }
                error={errors.role}
                helpText="Select the user's role in the system"
              />
            </div>
          </FormGrid>
        </FormCard>

        <FormCard
          title="Account Details"
          description="Additional user information"
        >
          <FormGrid cols={2} gap="md">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                User ID
              </label>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {user.id}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Created Date
              </label>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {new Date(user.createdAt).toLocaleDateString()}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Last Updated
              </label>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {new Date(user.updatedAt).toLocaleDateString()}
              </p>
            </div>
          </FormGrid>
        </FormCard>

        <FormActions align="between">
          <DeleteButton type="button" onClick={handleDelete}>
            Delete User
          </DeleteButton>
          <div className="flex space-x-3">
            <Link href="/users">
              <CancelButton>Cancel</CancelButton>
            </Link>
            <SubmitButton loading={isSubmitting} disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </SubmitButton>
          </div>
        </FormActions>
      </form>
    </div>
  );
}
