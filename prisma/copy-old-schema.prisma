// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User model for authentication and authorization
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String?
  role      Role     @default(CASHIER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  sales     Sale[]
  stockAdjustments StockAdjustment[]

  @@map("users")
}

// Product categories
model Category {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  products Product[]

  @@map("categories")
}

// Products/Inventory
model Product {
  id          Int      @id @default(autoincrement())
  name        String
  slug        String   @unique
  description String?
  barcode     String?  @unique
  sku         String?  @unique
  price       Float
  cost        Float?
  stock       Int      @default(0)
  minStock    Int      @default(0)
  categoryId  Int
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  category         Category          @relation(fields: [categoryId], references: [id])
  saleItems        SaleItem[]
  stockAdjustments StockAdjustment[]

  @@map("products")
}

// Sales transactions
model Sale {
  id          Int        @id @default(autoincrement())
  total       Float
  tax         Float?
  discount    Float?
  paymentType PaymentType @default(CASH)
  status      SaleStatus @default(COMPLETED)
  userId      Int
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  user      User       @relation(fields: [userId], references: [id])
  saleItems SaleItem[]

  @@map("sales")
}

// Individual items in a sale
model SaleItem {
  id        Int     @id @default(autoincrement())
  quantity  Int
  price     Float
  total     Float
  saleId    Int
  productId Int

  // Relations
  sale    Sale    @relation(fields: [saleId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@map("sale_items")
}

// Stock adjustments for inventory management
model StockAdjustment {
  id        Int            @id @default(autoincrement())
  quantity  Int
  type      AdjustmentType
  reason    String?
  productId Int
  userId    Int
  createdAt DateTime       @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id])
  user    User    @relation(fields: [userId], references: [id])

  @@map("stock_adjustments")
}

// Enums
enum Role {
  ADMIN
  MANAGER
  CASHIER
}

enum PaymentType {
  CASH
  CARD
  MOBILE
}

enum SaleStatus {
  PENDING
  COMPLETED
  CANCELLED
  REFUNDED
}

enum AdjustmentType {
  ADD
  REMOVE
  SET
}
